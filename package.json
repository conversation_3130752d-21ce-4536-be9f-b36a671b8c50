{"name": "ems-backend", "version": "1.0.0", "description": "this is the backend for the ems", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.3", "@supabase/supabase-js": "^2.43.4", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "firebase-admin": "^12.1.0", "html-pdf": "^3.0.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.9.13", "pdfkit": "^0.15.0", "puppeteer": "^22.10.0"}}